import { getCurrentInstance } from 'vue'
import ElementPlus from 'element-plus'
import { ElMessage } from 'element-plus'
// 注释掉自定义组件，先使用Element Plus原生组件
// import ElTransfer from '@/components/Element/Transfer'
// import ElTtooltip from '@/components/Element/Tooltip'
// import ElSelect from '@/components/Element/Select'
// import ElPopover from '@/components/Element/Popover'

export default function setupElementPlus(app) {
  app.use(ElementPlus, {
    size: 'small'
  })

  // 注册自定义组件 - 暂时注释掉，使用Element Plus原生组件
  // app.component(ElTransfer.name, ElTransfer)
  // app.component(ElTtooltip.name, ElTtooltip)
  // app.component(ElSelect.name, ElSelect)
  // app.component(ElPopover.name, ElPopover)

  // 配置全局消息方法
  app.config.globalProperties.$message = function(msg) {
    const options = typeof msg === 'string' || msg === null ? { message: msg, showClose: true } : { showClose: true, ...msg }
    return ElMessage({
      duration: 2000,
      ...options
    })
  }

  app.config.globalProperties.$message.success = function(msg) {
    return ElMessage.success({
      showClose: true,
      message: msg,
      duration: 2000
    })
  }

  app.config.globalProperties.$message.error = function(msg) {
    return ElMessage.error({
      showClose: true,
      message: msg,
      duration: 2000
    })
  }

  app.config.globalProperties.$message.warning = function(msg) {
    return ElMessage.warning({
      showClose: true,
      message: msg,
      duration: 2000
    })
  }
}
