<template>
  <div class="top-menu">
    <sidebar class="sidebar-container" />
  </div>
</template>
<script>
import Sidebar from '../Sidebar'
export default {
  name: 'TopMenu',
  components: {
    Sidebar
  }
}
</script>
<style lang="scss" scope>
@import "@/styles/variables.scss";
.top-menu {
  position: fixed;
  top: 0px;
  width: calc(100% - #{$nav-left-width});
  left: $nav-left-width;

  .el-menu {
    background: none;

    .el-menu-item {
      height: $nav-top-height;
      line-height: $nav-top-height;
      color: var(--main-font-color);

      &:not(.is-disabled):focus,
      &:not(.is-disabled):hover {
        color: var(--main-theme-color);
      }
    }
    .is-active {
      //顶部菜单选中颜色
      color: var(--main-theme-color);
      border-bottom-color: var(--main-theme-color);
      background: var(--content-bg-hover-color);
      padding: 0px 15px;
      font-weight: 500;
    }
    :has(.is-active) .el-menu-item{
       margin:0px!important;
     }
    .el-submenu__title {
      height: $nav-top-height;
      line-height: $nav-top-height;
    }
  }
}
</style>
