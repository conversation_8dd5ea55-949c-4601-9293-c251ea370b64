<script>
import { h } from 'vue'
import './desc.scss'
import classnames from 'classnames'
import { filterEmpty, getOptionProps, getSlot, getSlots } from '../../utils/props-utils'

import debounce from 'lodash/debounce'

export default {
  props: {
    column: {
      type: Number,
      default: 3
    },
    bordered: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tooltipContent: ''
    }
  },
  created() {
    this.activateTooltip = debounce(tooltip => tooltip.handleShowPopper(), 50)
  },
  methods: {
    /**
     * 按span 分组
     */
    chunkChild() {
      const slots = filterEmpty(getSlot(this))
      let i = 0
      const rows = [[]]
      let row = rows[0]
      slots.forEach((slot, idx) => {
        const props = getOptionProps(slot)
        const childs = getSlots(slot)
        const { span = 1 } = props
        if (i + span > this.column) {
          i = 0
          row = []
          rows.push(row)
        }
        row.push({
          span: idx + 1 === slots.length ? this.column - i : span,
          props,
          childs,
          slot
        })
        i += span
      })
      return rows
    },
    handleCellMouseEnter(event, slot) {
      const el = event.target
      // const ow = el.parentNode.offsetWidth
      // const sw = el.parentNode.scrollWidth
      const elw = el.offsetWidth
      const cw = el.childNodes[0]?.offsetWidth
      if (elw >= cw) return
      // const range = document.createRange();
      // range.selectNodeContents(el);
      // const rangeWidth = range.getBoundingClientRect().width;
      // const padding = (parseInt(getStyle(el, "paddingLeft")) || 0) + (parseInt(getStyle(el, "paddingRight")) || 0);
      const tooltip = this.$refs.tooltip

      // if (el.offsetWidth + 1 < rangeWidth + padding && tooltip) {
      this.tooltipContent = el.innerText || el.textContent
      tooltip.referenceElm = el
      tooltip.$refs.popper && (tooltip.$refs.popper.style.display = 'none')
      tooltip.doDestroy()
      tooltip.show()
      this.activateTooltip(tooltip)
      // }
    },

    handleCellMouseLeave(event) {
      const tooltip = this.$refs.tooltip
      tooltip && tooltip.hide()
      tooltip && tooltip.handleClosePopper()
    },
    renderItem({ span, props, childs, slot }) {
      const { label, ellipsis, calcWidth } = props
      const _label = childs.label || label
      const content = childs.default || h('span', { class: 'vone-desc-item__empty' }, '-')

      const ellipsisClz = classnames({
        'vone-desc-item__ellipsis': ellipsis !== false
      })

      const widthSty = {
        'width': 'calc(100% - 108px)',
        'overflow': 'hidden',
        'white-space': 'nowrap',
        'text-overflow': 'ellipsis'
      }

      if (this.bordered) {
        return [
          <th class='vone-desc-item__label text-33'>{_label}</th>,
          <td class={classnames('vone-desc-item__content text-31', ellipsisClz)} colspan={span * 2 - 1}>
            {content}
          </td>
        ]
      }

      return (
        <td class={classnames('vone-desc-item', ellipsisClz)} colspan={span}>
          <span class='vone-desc-item_section'>

            <span class='vone-desc-item__label text-33'>{_label}</span>
            <span class='vone-desc-item__content text-31' style={calcWidth ? widthSty : ''} on-mouseenter={ev => this.handleCellMouseEnter(ev, childs.default)} on-mouseleave={this.handleCellMouseLeave}>
              <span class='vone-desc-item__content__text'>{content}</span>
              {/* {content} */}
            </span>
          </span>
        </td>
      )
    }
  },
  render() {
    const { bordered } = this
    const arr = this.chunkChild()
    return (
      <table class={classnames('vone-desc', { 'vone-desc__bordered': bordered })}>
        <tbody>
          {arr.map(row => (
            <tr class='vone-desc-row'>{row.map(col => this.renderItem(col))}</tr>
          ))}
          <el-tooltip placement='top-start' arrow-offset={-1} ref='tooltip' content={this.tooltipContent}></el-tooltip>
        </tbody>
      </table>
    )
  }
}
</script>
