const capitalize = str => {
  let s = ''
  // 首字母小写
  s = str.charAt(0).toLowerCase() + str.slice(1)
  const letter = /[A-Z]/g.exec(s)
  if (letter) {
    return s.replace(letter[0], '-' + letter[0].toLowerCase())
  }
  return s
}

export default function setupComponents(app) {
  // 在Vite中使用import.meta.glob替代require.context
  const modules = import.meta.glob('./**/*.vue', { eager: true })

  Object.keys(modules).forEach(key => {
    let name = ''
    if (key.endsWith('index.vue')) {
      name = key.split('/')[1]
    } else {
      name = key.split('/')[2].replace(/\.vue$/, '')
    }
    // 转换组件名
    name = capitalize(name)
    const mod = modules[key]
    if (name === 'common-tab') return
    if (name === 'components') return
    if (name === 'link') return
    if (name === 'toolbar') return
    if (name === 'transfer') return
    if (name === 'embedded-page') return
    if (name === 'send-back') return
    if (name === 'handle-task') return
    if (name === 'palette') return
    if (name === 'tree-node') return
    // vone-components
    if (name === 'svg-icon') return
    if (name === 'element') return
    if (name === 'page-wrapper') {
      app.component(name, mod.default || mod)
    } else {
      app.component('vone-' + name, mod.default || mod)
    }
  })
}
