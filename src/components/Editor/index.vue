<template>
  <div class="vone-editor">
    <div v-if="preview" class="vone-editor__preview w-e-text" @click="imgclickFn($event)" v-html="value" />
    <div v-else ref="editorBox">
      <div v-if="!isFocus || disabled" :class="['focusBlur', disabled ? 'disabledDiv' :'']" placeholder="描述" v-html="value" />
      <div v-show="isFocus && !disabled" ref="editor" />
    </div>
    <el-image-viewer v-if="dialogVisible" :z-index="4000" :on-close="()=>{dialogVisible=false}" :url-list="imgList" />
  </div>
</template>

<script>

import Editor from 'wangeditor'
import { uploadFile } from '@/api/common'
export default {
  components: {
    // 使用Element Plus的图片查看器
    // 'el-image-viewer': () => import('element-plus/es/components/image-viewer')
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: null
    },
    preview: {
      type: Boolean,
      default: false
    },
    storageType: {
      type: String,
      default: ''
    },
    disabled: { // 是否是disabled状态，用于自定义表单，判断属性是否可编辑
      type: Boolean,
      default: false
    },
    isFold: { // isFold为false直接展示富文本输入器
      type: Boolean,
      default: true
    }

  },
  data() {
    return {
      editorVal: '',
      dialogVisible: false,
      imgList: [],
      isFocus: false
    }
  },
  computed: {
    el() {
      return this.$refs.editor
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (newValue === this.editorVal) return
        this._setValue()
        this.$emit('input', newValue)
      },
      immediate: true
    },
    preview: {
      handler(p) {
        if (!p) {
          this.$nextTick(() => this._initEditor())
        }
      },
      immediate: true
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.eidtclick)
  },
  mounted() {
    if (this.isFold) {
      document.addEventListener('click', this.eidtclick)
    } else {
      this.isFocus = true
    }
  },
  methods: {
    eidtclick(e) {
      const refClick = this.$refs.editorBox.contains(e.target)
      if (refClick) {
        this.isFocus = true
      } else {
        this.isFocus = false
      }
    },

    imgclickFn(e) {
      if (e.target.nodeName === 'IMG') {
        this.dialogVisible = true
        this.imgList = [e.target.src]
      }
    },
    _initEditor() {
      const storageType = this.storageType
      this.editor = new Editor(this.el)
      this.editor.config.focus = false
      this.editor.config.zIndex = 500
      this.editor.config.height = 200
      // this.editor.toolbarConfig.modalAppendToBody = true
      if (this.height) this.editor.config.height = this.height
      this.editor.config.onchange = (value) => {
        this.editorVal = value
        this.$emit('input', value)
      }
      this.editor.config.uploadImgShowBase64 = false
      this.editor.config.uploadImgMaxSize = 2 * 1024 * 1024 * 1024
      this.editor.config.uploadImgMaxLength = 5
      this.editor.config.customUploadImg = async function(resultFiles, insertImgFn) {
        resultFiles.forEach(e => {
          const fileForm = new FormData()
          fileForm.append('file', e, e.name.split('.')[0] + new Date().valueOf() + '.png')
          fileForm.append('bizType', 'BUG_FILE_UPLOAD')
          fileForm.append('storageType', storageType)
          uploadFile(fileForm).then(res => {
            if (res.isSuccess) {
              insertImgFn(`/api/base/base/file/noToken/download/${res.data.id}`)
            }
          })
        })
      }
      this.editor.config.uploadImgHooks = {
        before: function(xhr, editor, files) {
        }
      }
      this.editor.config.showLinkImg = false
      this.editor.config.customAlert = (s, t) => {
        switch (t) {
          case 'success':
            this.$message.success(s)
            break
          case 'info':
            this.$message.info(s)
            break
          case 'warning':
            this.$message.warning(s)
            break
          case 'error':
            this.$message.error(s)
            break
          default:
            this.$message.info(s)
            break
        }
      }
      this.editor.create()
      this._setValue()
    },
    _setValue() {
      if (this.editor) this.editor.txt.html(this.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.vone-editor {
  width: 100%;
  &__ {
    &preview {
      overflow: auto;
    }
  }
  ::v-deep {
    table {
      border-collapse: collapse;
      border: 1px solid var(--disabled-bg-color, #ebeef5);
      td,
      th {
        padding: 12px;
      }
    }
  }
}
.focusBlur {
  border: 1px solid var(--input-border-color);
  border-radius: 4px;
  padding: 1px 12px;
  min-height: 50px;
  max-height: 200px;
  overflow-y: auto;
  // color: antiquewhite;
}
.focusBlur:empty::before {
  content: attr(placeholder);
}

.disabledDiv {
  cursor: not-allowed !important;
  pointer-events: none;
  background-color: var(--disabled-bg-color);
  border: none;
}
</style>
