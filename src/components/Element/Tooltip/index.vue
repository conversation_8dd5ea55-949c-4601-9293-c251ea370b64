<script>
// import { Tooltip } from 'element-ui' // 注释掉element-ui导入
// TODO: 需要替换为Element Plus的Tooltip组件
import { debounce } from 'throttle-debounce'
// import Vue from 'vue' // Vue3中不需要这样导入
export default {
  name: 'ElTooltip',
  extends: Tooltip,
  props: {
    ...Tooltip.props
  },
  beforeCreate() {
    if (this.$isServer) return

    this.popperVM = new Vue({
      data: { node: '' },
      render(h) {
        return this.node
      }
    }).$mount()

    this.debounceClose = debounce(300, () => this.handleClosePopper())
  }
}
</script>
