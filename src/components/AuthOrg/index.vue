<template>
  <vone-drawer title="权限分配" :visible="visible" v-on="$listeners">
    <div class="switch-style">
      <span>父子节点联动</span>
      <el-switch
        v-model="switchValue"
        active-color="#21CC8D"
        inactive-color="#BCC2CB"
      />
    </div>
    <auth-tree style="padding: 0 16px;" v-bind="$attrs" :is-strictly="switchValue" @input="e => (checked = e)" v-on="$listeners" />
    <div slot="footer">

      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submitForm">确定</el-button>
    </div>
  </vone-drawer>
</template>

<script>
import AuthTree from './authTree.vue'
export default {
  components: {
    AuthTree
  },
  props:
   {
     visible: {
       type: Boolean,
       default: false
     },
     success: {
       type: Function,
       default: null
     }
   },
  data() {
    return {
      checked: [],
      saveLoading: false,
      switchValue: false
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
    },
    submitForm() {
      if (typeof this.success === 'function') {
        this.saveLoading = true
        try {
          if (this.success(Array.from(new Set(this.checked)))) {
            this.close()
          }
          this.saveLoading = false
        } catch (e) {
          return
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.switch-style {
  padding: 10px;
  background: #F5F5F5;
  margin: 16px;
  border-radius: 4px;
  border: 1px solid #F0F0F0;
  span {
    margin-right: 10px;
  }
}
</style>
