<template>
  <div class="page-wrapper has-background">
    <iframe ref="externalIframe" class="iframe-bpx" :src="src" :title="title" />
    <!-- <iframe ref="externalIframe" class="iframe-bpx" :src="src" title="" @load="iframeLoad" /> -->
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
export default {
  props: {},
  data() {
    return {
      src: '',
      title: null
    }
  },
  created() {
    const url = this.$route?.meta?.externalUrl || ''
    if (!url) {
      this.$message.warning('请检查配置的url是否正确')
      return
    }
    // const token = getToken() || null
    this.title = this.$route?.meta?.title || ''
    // this.src = token ? url + '?token=' + token : url
    this.src = url
  },
  mounted() {

  },
  methods: {
    iframeLoad() {
      const iframeWindow = this.$refs.externalIframe.contentWindow
      const data = { token: getToken() }
      iframeWindow.postMessage(data, '*')
    }
  }
}
</script>

<style lang='scss' scoped>
@import "@/styles/variables.scss";
.page-wrapper {
  padding: $main-padding;
  height: calc(100vh - 68px);
  border-radius: 4px;
}
.has-background {
  background-color: var(--main-bg-color);
}
.iframe-bpx {
  width: 100%;
  height: calc(100vh - 100px);
  border: none;
}
</style>
