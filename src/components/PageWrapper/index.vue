<template>
  <div :class="['page-wrapper', currentAppMenu.length > 0 ? 'has-header': '', hiddenBg? '' : 'has-background']">
    <slot />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  name: 'PageWrapper',
  props: {
    hiddenBg: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters([
      'currentAppMenu'
    ])
  }
}
</script>

<style lang='scss' scoped>
@import "@/styles/variables.scss";
.page-wrapper {
	padding: $main-padding $main-padding 10px $main-padding;
	min-height: calc(100vh - #{$nav-top-height} );
  border-radius: 4px;
  position: relative;
}
.has-background {
	background-color: var(--main-bg-color,#fff);
}
.has-header {
	min-height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin}   ) ;
  box-shadow: var(--main-bg-shadow);
}
</style>
