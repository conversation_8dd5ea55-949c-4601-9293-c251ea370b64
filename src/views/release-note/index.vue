<template>

  <div class="relesePage">
    <div class="noteMain">
      <div class="note_left">
        <h3 class="title">更新日志</h3>
        <div class="markdown-body">
          <p>更新日志内容将在这里显示</p>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import 'highlight.js/styles/github.css'
import 'github-markdown-css'

export default {
  components: {
  },
  data() {
    return {
      activeTab: null,
      fileNameList: [
        {
          sha: '20220518',
          name: '20220518'
        },
        {
          sha: '20220418',
          name: '20220418'
        },
        {
          sha: '20220318',
          name: '20220318'
        },
        {
          sha: '20220218',
          name: '20220218'
        },
        {
          sha: '20220118',
          name: '20220118'
        }
      ],
      mdCache: {}
    }
  },

  created() {

  },
  methods: {

  }

}
</script>

<style lang="scss" scoped>

.head-nav {
  background-color: var(--color-white);
  color: #fff;
  display: flex;
  justify-content: flex-end;
  padding: 0 20px;
  box-sizing: border-box;
}

.noteMain {
  width: 100%;
  // margin-top: 10px;
  background: var(--color-white);
  user-select: none;

  .note_left {
    width: 65%;
    margin-left: 12%;
    padding: 15px;
    box-sizing: border-box;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-weight: 600;
      font-size: 30px;
      line-height: 1.35;
    }

    ::v-deep .markdown-body {
      margin-left: 30px;
      position: relative;

      &::before {
        position: absolute;
        width: 12px;
        height: 12px;
        left: -30px;
        top: 6px;
        background: #1890ff;
        border-radius: 50%;
        z-index: 10;
      }
      &::after {
        position: absolute;
        height: 100%;
        top: 14px;
        left: -25px;
        bottom: 0;
        z-index: 8;
        border-left: 2px solid #e4e7ed;
      }

      p {
        margin-bottom: 10px !important;
        font-size: 16px;
        font-weight: bold;
      }

      ol,
      ul {
        padding-left: 3.3em;
        li {
          list-style-type: circle;
          font-size: 14px;
          font-weight: 400;
          p {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
  }
}
</style>
