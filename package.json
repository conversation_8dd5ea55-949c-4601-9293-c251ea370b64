{"name": "vone-va-web", "version": "1.0.0", "description": "vone-va-web", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "vuex": "^4.1.0", "element-plus": "^2.4.4", "axios": "0.21.4", "dayjs": "1.10.7", "lodash": "4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^2.3.4", "vite": "^2.9.18", "sass": "^1.69.5"}}