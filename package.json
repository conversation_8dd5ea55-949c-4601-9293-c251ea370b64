{"name": "vone-va-web", "version": "1.0.0", "description": "vone-va-web", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "vuex": "^4.1.0", "element-plus": "^2.4.4", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "qs": "^6.11.2", "echarts": "^5.4.3", "codemirror": "^5.65.16", "vue-simple-uploader": "^0.7.6", "vue-clipboard2": "^0.3.3", "vue-cropper": "^0.5.8", "vue-grid-layout": "^2.3.12", "vue-json-pretty": "^2.2.4", "vuedraggable": "^4.1.0", "vue-uuid": "^3.0.0", "mavon-editor": "^3.0.0", "vue-json-viewer": "^3.0.4", "vue-fullscreen": "^2.6.1", "vue-float-action-button": "^0.7.9", "vxe-table": "^4.5.0", "vxe-table-plugin-element": "^3.0.7", "xe-utils": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.4", "sass": "^1.69.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "unplugin-auto-import": "^0.17.2", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vite-plugin-svg-icons": "^2.0.1", "mockjs": "^1.1.0"}}