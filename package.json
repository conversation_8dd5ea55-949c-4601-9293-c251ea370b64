{"name": "vone-va-web", "version": "1.0.0", "description": "vone-va-web", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "analyzer": "use_analyzer=true npm run build:prod"}, "dependencies": {"@antv/g6": "4.6.17", "@antv/layout": "^0.3.22", "@aomao/engine": "^2.10.19", "@aomao/plugin-alignment": "^2.10.0", "@aomao/plugin-backcolor": "^2.10.0", "@aomao/plugin-bold": "^2.10.0", "@aomao/plugin-code": "^2.10.0", "@aomao/plugin-file": "^2.10.0", "@aomao/plugin-fontcolor": "^2.10.0", "@aomao/plugin-fontfamily": "^2.10.0", "@aomao/plugin-fontsize": "^2.10.0", "@aomao/plugin-heading": "^2.10.0", "@aomao/plugin-hr": "^2.10.0", "@aomao/plugin-image": "^2.10.0", "@aomao/plugin-indent": "^2.10.0", "@aomao/plugin-italic": "^2.10.0", "@aomao/plugin-line-height": "^2.10.0", "@aomao/plugin-mark-range": "^2.10.0", "@aomao/plugin-math": "^2.10.0", "@aomao/plugin-mention": "^2.10.0", "@aomao/plugin-orderedlist": "^2.10.0", "@aomao/plugin-paintformat": "^2.10.0", "@aomao/plugin-quote": "^2.10.0", "@aomao/plugin-redo": "^2.10.0", "@aomao/plugin-removeformat": "^2.10.0", "@aomao/plugin-selectall": "^2.10.0", "@aomao/plugin-status": "^2.10.0", "@aomao/plugin-strikethrough": "^2.10.0", "@aomao/plugin-sub": "^2.10.0", "@aomao/plugin-sup": "^2.10.0", "@aomao/plugin-table": "^2.10.0", "@aomao/plugin-tasklist": "^2.10.0", "@aomao/plugin-underline": "^2.10.0", "@aomao/plugin-undo": "^2.10.0", "@aomao/plugin-unorderedlist": "^2.10.0", "@aomao/plugin-video": "^2.10.0", "@aomao/plugin-yjs": "^1.0.5", "@aomao/plugin-yjs-websocket": "1.0.7", "@bpmn-io/replace-ids": "^0.2.0", "@faker-js/faker": "^7.6.0", "@logicflow/core": "^1.1.12", "@logicflow/extension": "^1.1.12", "@popperjs/core": "^2.11.5", "@riophae/vue-treeselect": "0.4.0", "autosize": "^6.0.1", "axios": "0.21.4", "bpmn-js": "^7.5.0", "bpmn-js-bpmnlint": "^0.18.0", "bpmnlint": "^7.8.0", "classnames": "2.3.1", "code-inspector-plugin": "^0.20.12", "codemirror": "5.58.1", "codemirror-minimap": "^1.0.6", "core-js": "3.6.5", "dayjs": "1.10.7", "dhtmlx-gantt": "^7.1.12", "echarts": "5.1.0", "element-resize-detector": "1.2.2", "element-ui": "2.15.6", "gantt-elastic": "1.0.12", "gantt-elastic-header": "0.1.11", "github-markdown-css": "^5.1.0", "highlight.js": "11.8.0", "html2canvas": "1.3.4", "js-base64": "3.7.2", "jspdf": "2.5.0", "lodash": "4.17.21", "markdown-it": "^13.0.1", "marked": "^4.0.17", "mavon-editor": "2.9.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "postcss-pxtorem": "^5.1.1", "snowflake-id": "1.1.0", "spark-md5": "3.0.2", "store": "2.0.12", "swiper": "7.3.1", "vscode-icons-js": "11.6.1", "vue": "2.6.10", "vue-clipboard2": "0.3.3", "vue-cropper": "0.5.6", "vue-float-action-button": "^0.7.9", "vue-fullscreen": "^2.5.2", "vue-grid-layout": "2.3.12", "vue-json-pretty": "^1.8.3", "vue-json-viewer": "2.2.20", "vue-router": "3.3.4", "vue-simple-uploader": "0.7.6", "vue-uuid": "2.1.0", "vue-virtual-scroll-list": "^2.3.4", "vuedraggable": "2.24.3", "vuex": "3.1.0", "vxe-table": "3.7.0", "vxe-table-plugin-element": "^1.11.2", "wangeditor": "4.7.9", "xe-utils": "^3.5.7", "xlsx": "^0.18.5", "xterm": "4.14.1", "xterm-addon-fit": "0.5.0", "yjs": "13.5.50"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.28.0", "@vue/cli-plugin-babel": "4.5.15", "@vue/cli-plugin-eslint": "4.5.15", "@vue/cli-plugin-unit-jest": "4.5.15", "@vue/cli-service": "4.5.15", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-import": "^1.13.6", "babel-plugin-transform-remove-console": "^6.9.4", "bpmnlint-loader": "^0.1.5", "chalk": "2.4.2", "compression-webpack-plugin": "^5.0.2", "connect": "3.6.6", "element-theme-chalk": "^2.15.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "postcss-discard-empty": "^7.0.1", "raw-loader": "^4.0.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vite-plugin-style-import": "^2.0.0", "vue-markdown-loader": "^2.5.0", "vue-template-compiler": "2.6.10", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=14.17.6", "npm": ">= 6.14.15"}, "license": "MIT"}