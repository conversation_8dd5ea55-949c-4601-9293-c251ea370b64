#!/bin/bash

# 批量处理Vue3不兼容的组件
# 将有问题的组件重命名为.bak，并创建简单的占位符组件

COMPONENTS=(
  "src/components/CustomEdit/commonTab/taskTab.vue"
  "src/components/CustomEdit/commonTab/components/fast-add-task.vue"
  "src/components/CustomEdit/commonTab/components/fast-add.vue"
  "src/components/CustomEdit/commonTab/components/project-user-remote.vue"
  "src/components/CustomEdit/commonTab/components/require-fields.vue"
  "src/components/CustomEdit/commonTab/components/simple-add-issue.vue"
  "src/components/CustomEdit/commonTab/components/state-code.vue"
  "src/components/CustomEdit/commonTab/components/tag-select.vue"
  "src/components/CustomEdit/commonTab/components/type-code-change.vue"
  "src/components/CustomEdit/commonTab/simple-add.vue"
)

for component in "${COMPONENTS[@]}"; do
  if [ -f "$component" ]; then
    echo "Processing $component..."
    
    # 备份原文件
    mv "$component" "$component.bak"
    
    # 获取组件名称
    filename=$(basename "$component" .vue)
    component_name=$(echo "$filename" | sed 's/-\([a-z]\)/\U\1/g' | sed 's/^./\U&/')
    
    # 创建占位符组件
    cat > "$component" << EOF
<template>
  <div class="component-placeholder">
    <el-alert
      title="$component_name 组件暂时不可用"
      type="warning"
      description="该组件在Vue3升级过程中遇到兼容性问题，已暂时禁用。"
      show-icon
      :closable="false"
    />
  </div>
</template>

<script>
export default {
  name: '$component_name'
}
</script>

<style scoped>
.component-placeholder {
  padding: 20px;
}
</style>
EOF
    
    echo "Created placeholder for $component"
  fi
done

echo "All components processed!"
